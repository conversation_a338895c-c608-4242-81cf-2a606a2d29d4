/*
 * @file AIHelper 工具包
 * <AUTHOR>
 * @date 2025-07-28 14:36:02
 */

import bmls from '@baidu/bdmedialive-scheme';

function naFn(params = {}) {
    const encodedParams = encodeURIComponent(JSON.stringify(params));
    const url = `bmls://bdmedialive/common/clueWinnowerEvent?params=${encodedParams}`;
    location.href = url;
}

export const aiHelperNativeUtils = {
    sendMsg: (message = '') => naFn({
        type: '3',
        message
    }),
    closeWebview: () => {
        bmls.common.closeWebview();
    },
    openCommentPanel: () => {
        bmls.common.openCommentPanel();
    },
    getRoomInfo: async () => {
        return await bmls.common.getRoomInfo();
    },
    // eslint-disable-next-line no-unused-vars
    openVideoPlayer: (playUrl, coverImgUrl, videoId) => {
        const encodedParams = encodeURIComponent(JSON.stringify({playUrl, coverImgUrl, videoId}));
        location.href = `bmls://bdmedialive/clue/openVideoPlayer?params=${encodedParams}`;
    },
    openAnyDoor: (url) => {
        location.href = url;
    }
};

export function format(queryRoomVideos) {
    if (!queryRoomVideos || !queryRoomVideos.videos) {
        return [];
    }

    return queryRoomVideos.videos.map(({
        title, videoSummary, videoUrl, videoCoverImg, questions, videoId
    }, index) => {
        return {
            id: index + 1,
            title,
            content: videoSummary,
            videoUrl,
            videoCover: videoCoverImg,
            questions: [
                ...questions.map(q => ({
                    id: videoId,
                    text: q.question
                })),
                {
                    id: 0, // 按照0
                    text: '我想问其他问题',
                    isOther: true
                }
            ]
        };
    });
}