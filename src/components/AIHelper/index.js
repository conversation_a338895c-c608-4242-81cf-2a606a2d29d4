/* eslint-disable max-len */
/*
 * @file 直播小助手
 * <AUTHOR>
 * @date 2025-07-25 11:07:22
 */

import React, {useState, useRef, useEffect, useCallback} from 'react';
import classNames from 'classnames';
import './index.less';
import {useRequest} from 'ahooks';
import {PageStore} from '@/pages/liveshow/service/register';
import {anyMountResult} from 'mock/debug';
import VideoPlayer from './videoPlayer';
import {aiHelperNativeUtils, format} from './utils';
import {requestRoomEnterInfoWithAntiCheats} from './api';
import {queryRoomVideos} from './mock';

const InnerComp = ({
    sections = [],
    bottomCard = {},
    onConsult = () => {},
    onQuestionClick = () => {},
    isH5
}) => {
    const [activeTab, setActiveTab] = useState(0);
    const containerRef = useRef(null);
    const sectionRefs = useRef([]);
    const tabsRef = useRef(null);
    const tabsScrollRef = useRef(null);
    const tabItemRefs = useRef([]);

    // 添加状态来控制是否正在进行程序化滚动
    const isScrollingProgrammatically = useRef(false);
    // 添加定时器引用来防抖滚动事件
    const scrollTimeoutRef = useRef(null);


    // 滚动TAB到可见区域
    const scrollTabIntoView = (index) => {
        const tabsScrollContainer = tabsScrollRef.current;
        const targetTab = tabItemRefs.current[index];

        if (!tabsScrollContainer || !targetTab) {
            return;
        }

        const containerRect = tabsScrollContainer.getBoundingClientRect();
        const tabRect = targetTab.getBoundingClientRect();

        // 计算tab相对于滚动容器的位置
        const tabLeft = tabRect.left - containerRect.left + tabsScrollContainer.scrollLeft;
        const tabRight = tabLeft + tabRect.width;

        const containerWidth = containerRect.width;
        const scrollLeft = tabsScrollContainer.scrollLeft;
        const scrollRight = scrollLeft + containerWidth;

        // 如果tab不完全可见，则滚动到合适位置
        if (tabLeft < scrollLeft) {
            // tab在左侧被遮挡，滚动到左边
            tabsScrollContainer.scrollTo({
                left: tabLeft - 16, // 16px 边距
                behavior: 'smooth'
            });
        }
        else if (tabRight > scrollRight) {
            // tab在右侧被遮挡，滚动到右边
            tabsScrollContainer.scrollTo({
                left: tabRight - containerWidth + 16, // 16px 边距
                behavior: 'smooth'
            });
        }
    };

    // 处理tab点击
    const handleTabClick = (index) => {
        // 立即设置active状态，避免延迟
        setActiveTab(index);

        // 设置程序化滚动标志，防止滚动事件干扰
        isScrollingProgrammatically.current = true;

        // 滚动TAB到可见区域
        scrollTabIntoView(index);

        // 滚动到对应section
        if (sectionRefs.current[index]) {
            const offsetTop = sectionRefs.current[index].offsetTop;
            // 考虑tab栏高度
            const tabsHeight = tabsRef.current ? tabsRef.current.offsetHeight : 0;
            containerRef.current.scrollTo({
                top: offsetTop - tabsHeight - 20, // 额外20px间距
                behavior: 'smooth'
            });

            // 滚动完成后重置标志（估算滚动时间）
            setTimeout(() => {
                isScrollingProgrammatically.current = false;
            }, 800); // 给smooth滚动足够的时间完成
        }
        else {
            isScrollingProgrammatically.current = false;
        }
    };
    // 处理滚动事件，更新active tab
    const handleScroll = useCallback(() => {
        // 如果正在进行程序化滚动，忽略滚动事件
        if (isScrollingProgrammatically.current) {
            return;
        }

        if (!containerRef.current || !sectionRefs.current.length) {
            return;
        }

        // 清除之前的定时器
        if (scrollTimeoutRef.current) {
            clearTimeout(scrollTimeoutRef.current);
        }

        // 使用防抖来减少频繁的状态更新
        scrollTimeoutRef.current = setTimeout(() => {
            const scrollTop = containerRef.current.scrollTop;
            const tabsHeight = tabsRef.current ? tabsRef.current.offsetHeight : 0;
            const containerHeight = containerRef.current.clientHeight;

            // 计算当前应该激活的tab - 当内容显示一半时就激活
            let newActiveTab = 0;
            for (let i = 0; i < sectionRefs.current.length; i++) {
                const section = sectionRefs.current[i];
                if (section) {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionMiddle = sectionTop + sectionHeight / 2;

                    // 当section的中点进入可视区域时激活该tab
                    const viewportTop = scrollTop + tabsHeight;
                    const viewportBottom = scrollTop + containerHeight;

                    if (sectionMiddle >= viewportTop && sectionMiddle <= viewportBottom) {
                        newActiveTab = i;
                        break;
                    }
                }
            }

            if (newActiveTab !== activeTab) {
                setActiveTab(newActiveTab);
            }
        }, 50); // 50ms防抖延迟
    }, [activeTab]);

    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
            return () => {
                container.removeEventListener('scroll', handleScroll);
                // 清理定时器
                if (scrollTimeoutRef.current) {
                    clearTimeout(scrollTimeoutRef.current);
                }
            };
        }
    }, [handleScroll]);



    return (
        <div className="ai-helper-container">
            {/* 固定的tab栏 */}
            <div className="tabs-container" ref={tabsRef}>
                <div className="tabs-wrapper">
                    <div className="main-title">讲解回放</div>
                    <div className="tabs-scroll-container" ref={tabsScrollRef}>
                        <div className="tabs">
                            {sections.map((section, index) => (
                                <div
                                    key={section.id}
                                    ref={el => tabItemRefs.current[index] = el}
                                    className={classNames('tab-item', {
                                        active: activeTab === index
                                    })}
                                    onClick={() => handleTabClick(index)}
                                >
                                    {section.title}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* 滚动内容区域 */}
            <div className="content-container" ref={containerRef}>
                {sections.map((section, index) => (
                    <div
                        key={section.id}
                        className="content-section"
                        ref={el => sectionRefs.current[index] = el}
                    >
                        <div className="section-content">
                            <p className="content-text">{section.content}</p>

                            <div className="video-container">
                                <div
                                    className="video-wrapper"
                                >
                                    <VideoPlayer index={index} section={section} isH5={isH5} />
                                </div>
                            </div>

                            {/* 每个section都有自己的猜你想问 */}
                            {!isH5 && section.questions && section.questions.length > 0 && (
                                <div className="questions-section">
                                    <div className="question-prompt">
                                        猜你想问：
                                    </div>
                                    <div className="questions-list">
                                        {section.questions.map((question) => (
                                            <div
                                                key={question.id}
                                                className="question-item"
                                                onClick={() => onQuestionClick(question, section)}
                                            >
                                                {question.text}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            {/* 底部吸底卡片 */}
            <div className="bottom-card">
                <div className="card-content">
                    <div className="card-left">
                        <img src={bottomCard.icon} alt="医生头像" className="doctor-icon" />
                        <div className="card-info">
                            <div className="card-title">{bottomCard.title}</div>
                            {bottomCard.subtitle && <div className="card-subtitle">{bottomCard.subtitle}</div>}
                        </div>
                    </div>
                    <button className="consult-button" onClick={onConsult}>
                        {bottomCard.buttonText}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default function AIHelper({host = 'h5', h5Options = {}, nativeOptions = {}}) {
    const isH5 = host === 'h5';
    // 请求任意门的接口
    const {data: {
        // anyMountResult = {},
        dynamicPeoplesResult = {}
        // queryRoomVideos = {}
    } = {}} = useRequest(requestRoomEnterInfoWithAntiCheats, {
        defaultParams: [PageStore.roomId, PageStore.deviceId]
    });
    // TODO
    console.log(anyMountResult, dynamicPeoplesResult, 'anyMountResult');

    const sections = format(queryRoomVideos);

    const bottomCard = {
        icon: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg',
        title: '领取白癜风修复白斑中药方',
        // subtitle: '1000人已预约',
        buttonText: '立即咨询'
    };

    // 咨询按钮点击处理
    const handleConsult = async () => {
        if (isH5) {
            // H5 打开任意门
        }
        else {
            const res = await aiHelperNativeUtils.getRoomInfo();
            const clueExt = res.data.clueExt || {};
            const url = clueExt.url;
            console.log(clueExt, url, '线索直播附加数据');
            aiHelperNativeUtils.openAnyDoor(url);
        }
    };

    // 问题点击处理
    const handleQuestionClick = question => {
        const isOther = question.isOther; // 猜你想问
        if (isOther) {
            // 关闭抽屉
            aiHelperNativeUtils.closeWebview();
            // 调起评论
            aiHelperNativeUtils.openCommentPanel();
        }
        else {
            // 发送消息
            aiHelperNativeUtils.sendMsg(question.text);
            // 关闭抽屉
            aiHelperNativeUtils.closeWebview();
        }
    };

    return (
        <div style={{height: '100%'}}>
            <InnerComp
                sections={sections}
                bottomCard={bottomCard}
                onConsult={handleConsult}
                onQuestionClick={handleQuestionClick}
                isH5={isH5}
            />
        </div>
    );
}
