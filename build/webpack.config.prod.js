/* eslint-disable */
const os = require('os');
const _ = require('lodash');
const path = require('path');
const webpack = require('webpack');
const merge = require('webpack-merge');
const threadLoader = require('thread-loader');
const TerserPlugin = require('terser-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const {
    WebpackManifestPlugin
} = require('webpack-manifest-plugin');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');

const baseWebpackConfig = require('./webpack.config.base');
const utils = require('./utils');
const config = require('./project.config');
const babelIncludeArray = require('./babelIncludeArray');

const cpusLen = Math.min(4, os.cpus().length);
const workerPoolBabel = {
    // 产生的 worker 的数量，默认是 (cpu 核心数 - 1)，或者，
    // 在 require('os').cpus() 是 undefined 时回退至 1
    workers: cpusLen,

    // 一个 worker 进程中并行执行工作的数量
    // 默认为 20
    workerParallelJobs: 5,

    // 额外的 node.js 参数
    workerNodeArgs: ['--max-old-space-size=512'],

    // 允许重新生成一个僵死的 work 池
    // 这个过程会降低整体编译速度
    // 并且开发环境应该设置为 false
    poolRespawn: true,

    // 闲置时定时删除 worker 进程
    // 默认为 500（ms）
    // 可以设置为无穷大，这样在监视模式(--watch)下可以保持 worker 持续存在
    poolTimeout: 2000,

    // 池分配给 worker 的工作数量
    // 默认为 200
    // 降低这个数值会降低总体的效率，但是会提升工作分布更均一
    poolParallelJobs: 10,

    // 池的名称
    // 可以修改名称来创建其余选项都一样的池
    name: 'workerPool'
};
const workerPoolLess = {
    ...workerPoolBabel,
    workers: cpusLen / 2,
    name: 'workerPoolLess'
};

threadLoader.warmup(workerPoolBabel, ['babel-loader', '@babel/preset-env']);
threadLoader.warmup(workerPoolLess, ['less-loader', 'postcss-loader', 'css-loader']);

const { business, projectName, productName } = config;
const staticPathPrefix = `static/${business}/${productName}/${projectName}`;
const appsPathPrefix = `apps/${business}/${productName}/${projectName}`;

const isEslint = () => {
    let rules = [];
    let plugins = [];
    if (config.build.eslint) {
        plugins.push(
            new ESLintPlugin({
                extensions: ['js', 'jsx'],
                context: path.join(__dirname, '..', 'src'),
                quiet: true,
                fix: true,
                cache: false,
                formatter: require('eslint-friendly-formatter'),
                emitWarning: false,
                threads: cpusLen / 2
            })
        );
    }
    return {
        rules,
        plugins
    };
};

const prodConfig = {
    devtool: 'hidden-source-map',
    performance: {
        maxAssetSize: 1024 * 1024,
        maxEntrypointSize: 1024 * 1024
    },
    output: {
        filename: `${staticPathPrefix}/scripts/[name].[chunkhash:8].js`,
        chunkFilename: `${staticPathPrefix}/scripts/chunks/[name].[chunkhash:8].js`
    },
    module: {
        rules: [
            ...isEslint().rules,
            {
                test: /\.jsx?$/,
                include: babelIncludeArray,
                loader: [
                    {
                        loader: 'thread-loader',
                        options: workerPoolBabel
                    },
                    {
                        loader: 'babel-loader',
                        options: {
                            cacheDirectory: true
                        }
                    }
                ]
            },
            {
                test: /\.module\.less$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'thread-loader',
                        options: workerPoolLess
                    },
                    {
                        loader: 'css-loader',
                        options: {
                            modules: true,
                            localIdentName: '[folder]_[name]_[local]_[hash:base64:5]',
                            importLoaders: 2,
                            minimize: true
                        }
                    },
                    'postcss-loader',
                    'less-loader'
                ]
            },
            {
                test: /\.less$/,
                exclude: /\.module\.less$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'thread-loader',
                        options: workerPoolLess
                    },
                    {
                        loader: 'css-loader',
                        options: {
                            modules: true,
                            localIdentName: '[local]',
                            importLoaders: 2,
                            minimize: true
                        }
                    },
                    'postcss-loader',
                    'less-loader'
                ]
            },
            {
                test: /\.jpe?g$|\.gif$|\.png$|\.svg$|\.ico$/,
                use: [
                    {
                        loader: 'url-loader',
                        options: {
                            limit: 25000,
                            name: `${staticPathPrefix}/images/[name].[hash:8].[ext]`
                        }
                    }
                ]
            }
        ]
    },
    optimization: {
        // noEmitOnErrors: true,
        runtimeChunk: {
            name: entrypoint => `runtime~${entrypoint.name}`
        },
        splitChunks: {
            chunks: 'all',
            maxInitialRequests: 5,
            maxAsyncRequests: 12, // for HTTP2
            minChunks: 5,
            cacheGroups: {}
        },
        minimize: true,
        minimizer: [
            // js 压缩处理
            new TerserPlugin({
                parallel: 4, // 开启多核并行处理压缩，加快速度
                terserOptions: {
                    sourceMap: false,
                    compress: {
                        drop_debugger: true,
                        drop_console: false,
                        // pure_funcs: ['console.log'],
                        // global_defs: {
                        //     '@alert': 'console.log'
                        // },
                        passes: 2
                    }, // 不显示警告信息
                    output: {
                        beautify: false
                    }
                }
            }),
            // css 压缩处理
            new OptimizeCSSAssetsPlugin({
                assetNameRegExp: /\.css\.*(?!.*map)/g,
                cssProcessorOptions: {
                    safe: true,
                    autoprefixer: false,
                    discardComments: {
                        removeAll: true
                    }
                }
            })
        ]
    },
    plugins: [
        new CleanWebpackPlugin(),
        // 保持缓存
        new webpack.HashedModuleIdsPlugin({
            hashFunction: 'sha256',
            hashDigest: 'hex',
            hashDigestLength: 20
        }),
        ...isEslint().plugins,
        new MiniCssExtractPlugin({
            filename: `${staticPathPrefix}/styles/[name].[contenthash:8].css`,
            chunkFilename: `${staticPathPrefix}/styles/chunks/[name].[contenthash:8].css`
        }),
        ...utils.htmlWebpackPluginList,
        new ScriptExtHtmlWebpackPlugin({
            inline: /runtime~.*\.js$/,
            custom: {
                test: /\.js$/,
                attribute: 'crossorigin',
                value: 'anonymous'
            }
        })
    ]
};

const webpackConfig = merge(baseWebpackConfig, prodConfig);

// 开启后会在build完成后自动打开localhost:8888页面，显示所有生成文件的大小与依赖包含情况
if (config.build.bundleAnalyzerReport) {
    const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
    webpackConfig.plugins.push(new BundleAnalyzerPlugin());
}

let entryForNode = _.cloneDeep(baseWebpackConfig.entry);
// entryForNode = Object.keys(entryForNode).reduce((pre, cur) => {
//     pre[cur] = entryForNode[cur].slice(entryForNode[cur].indexOf('babel-polyfill') + 1);
//     return pre;
// }, {});

const webpackConfigForSSR = merge({
    customizeObject(a, b, key) {
        if (key === 'entry') {
            return b;
        }
    }
})(baseWebpackConfig, {
    target: 'node',
    entry: entryForNode,
    output: {
        publicPath: '/',
        filename: `${appsPathPrefix}/scripts/[name].[chunkhash:8].node.js`
    },
    module: {
        strictExportPresence: true,
        rules: [
            {
                test: /\.jsx?$/,
                include: babelIncludeArray,
                loader: [
                    {
                        loader: 'thread-loader',
                        options: workerPoolBabel
                    },
                    {
                        loader: 'babel-loader',
                        options: {
                            cacheDirectory: true
                        }
                    }
                ]
            },
            {
                test: /\.module\.less$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'thread-loader',
                        options: workerPoolLess
                    },
                    {
                        loader: 'css-loader',
                        options: {
                            modules: true,
                            localIdentName: '[folder]_[name]_[local]_[hash:base64:5]',
                            importLoaders: 2,
                            minimize: true
                        }
                    },
                    'postcss-loader',
                    'less-loader'
                ]
            },
            {
                test: /\.less$/,
                exclude: /\.module\.less$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'thread-loader',
                        options: workerPoolLess
                    },
                    {
                        loader: 'css-loader',
                        options: {
                            modules: true,
                            localIdentName: '[local]',
                            importLoaders: 2,
                            minimize: true
                        }
                    },
                    'postcss-loader',
                    'less-loader'
                ]
            },
            {
                test: /\.jpe?g$|\.gif$|\.png$|\.svg$|\.ico$/,
                use: [
                    {
                        loader: 'url-loader',
                        options: {
                            limit: 25000,
                            name: `${staticPathPrefix}/images/[name].[hash:8].[ext]`
                        }
                    }
                ]
            },
            {
                test: /\.(ttf|eot|woff|woff2|otf)$/,
                loader: 'file-loader',
                options: {
                    // limit: 25000,
                    name: `${appsPathPrefix}/fonts/[name].[hash:8].node.[ext]`
                }
            }
        ]
    },

    plugins: [
        new webpack.DefinePlugin({
            'process.env.isServer': JSON.stringify(true)
        }),
        new webpack.HashedModuleIdsPlugin({
            hashFunction: 'sha256',
            hashDigest: 'hex',
            hashDigestLength: 20
        }),
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: 'src/pages/**/server/**/*',
                    to: `${appsPathPrefix}/server`,
                    transformPath(targetPath) {
                        /*
                         * 路径裁剪
                         * apps/live/media/clue/server/src/pages/contact/server/index.js'
                         * to
                         * apps/live/media/clue/server/contact/index.js'
                         */

                        const to = `${appsPathPrefix}/server`;
                        let relativeEntryPath = path.relative(to, targetPath);
                        relativeEntryPath = relativeEntryPath.replace(/src\/pages/, '').replace(/\/server/, '');

                        return path.join(to, relativeEntryPath).toLowerCase();
                    },
                    globOptions: {
                        dot: true
                    }
                }
            ],
            options: {
                concurrency: 100
            }
        }),
        new MiniCssExtractPlugin({
            filename: `${appsPathPrefix}/styles/[name].[contenthash:8].node.css`,
            chunkFilename: `${appsPathPrefix}/styles/chunks/[name].[contenthash:8].node.css`
        }),
        new WebpackManifestPlugin({
            fileName: `${appsPathPrefix}/manifest.node.json`,
            map(item) {
                const regExp = /^src\/pages\/([^/]+)\/server\/([^/]+.js$)/;
                const match = item.name.match(regExp);

                if (match) {
                    item.name = `server.${match[1]}.${match[2]}`.toLowerCase();
                }

                return item;
            }
        })
    ]
});

module.exports =  Object.keys(entryForNode).length ? [webpackConfig, webpackConfigForSSR] : [webpackConfig];
